<script setup>
import SimplyBreadcrumb from "@/views/components/SimplyBreadcrumb/index.vue";

import imageIcon from "@/assets/images/detail/image.png";
import wordIcon from "@/assets/images/detail/word.png";
import pdfIcon from "@/assets/images/detail/pdf.png";

// 如果需要路由功能，可以取消注释
const route = useRoute()
// const router = useRouter()

const type = route.query.type

// 模拟数据
const complaintData = ref({
  // 投诉人信息
  complainant: {
    name: "张三",
    phone: "138****5678",
    identity: "学生家长",
    gender: "男",
  },
  // 投诉学校信息
  school: {
    region: "江苏省南京市玄武区",
    schoolName: "南京市第一中学",
    schoolType: "中学",
  },
  // 具体问题
  problem: {
    reportTime: "2025.07.15 14:30:22",
    category: "膳食经费",
    description:
      "举报南京市第一中学膳食经费有问题，小学部膳食质量差，请有关部门介入调查，请相关部门严查。",
    attachments: [
      { name: "举报校内问题片1.jpg", url: "#", type: "img" },
      { name: "举报校内问题片2.jpg", url: "#", type: "img" },
    ],
  },
  // 分办信息
  assignment: {
    assignTime: "2025.07.15 14:30:22",
    assignedTo: "请及时处理该问题，并确保内部高效处理。",
  },
  // 回复信息
  replies: [
    {
      id: 1,
      department: "鼓楼区教育局",
      replyTime: "2025-07-18 16:45:12",
      handlerName: "李四",
      handlerPhone: "13370898798",
      content: "接到投诉后，我们立即组织人员对该区域进行了清理，并加强了厨房环境卫生管理。今后加强食堂卫生管理，确保类似问题不再发生。",
      attachments: [
        { name: "处理后相关照片1.jpg", url: "#", type: "img" },
        { name: "处理情况说明.doc", url: "#", type: "doc" },
        { name: "处理情况说明.doc", url: "#", type: "doc" }
      ]
    }
  ],
  // 审核信息
  reviews: [
    {
      id: 1,
      status: "未通过",
      reviewer: "审核时间：2025-07-20 09:15:30",
      content: "请继续跟进处理，但只是表面处理，没有从根本上解决问题。",
      statusColor: "#FF6B6B"
    }
  ],
  // 审核回复
  reviewReplies: [
    {
      id: 1,
      department: "鼓楼区教育局",
      replyTime: "2025-07-18 16:45:12",
      handlerName: "李四",
      handlerPhone: "13370898798",
      content: "接到投诉后，我们立即组织人员对该区域进行了清理，并加强了厨房环境卫生管理。今后加强食堂卫生管理，确保类似问题不再发生。",
      attachments: [
        { name: "处理后相关照片1.jpg", url: "#", type: "img" },
        { name: "处理情况说明.doc", url: "#", type: "doc" },
        { name: "处理情况说明.doc", url: "#", type: "doc" }
      ]
    }
  ],
  // 最终审核
  finalReview: {
    status: "已通过",
    reviewer: "审核时间：2025-07-20 09:15:30",
    statusColor: "#52C41A"
  }
});

// 下载附件
const downloadAttachment = (attachment) => {
  console.log("下载附件:", attachment.name);
  // 这里实现下载逻辑
};
</script>

<template>
  <div class="detail app-container">
    <SimplyBreadcrumb />
    <section class="content px-[24px] py-[20px] bg-[#fff] rounded-[10px]">
      <!-- 投诉人信息 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img
            src="@/assets/images/detail/person.png"
            alt=""
            width="12"
            height="14"
          />
          <span class="title-text">投诉人信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名</span>
            <span class="value">{{ complaintData.complainant.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机</span>
            <span class="value">{{ complaintData.complainant.phone }}</span>
          </div>
          <div class="info-item">
            <span class="label">身份</span>
            <span class="value">{{ complaintData.complainant.identity }}</span>
          </div>
          <div class="info-item">
            <span class="label">性别</span>
            <span class="value">{{ complaintData.complainant.gender }}</span>
          </div>
        </div>
      </div>

      <!-- 投诉学校信息 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img
            src="@/assets/images/detail/school.png"
            alt=""
            width="12"
            height="14"
          />
          <span class="title-text">投诉学校信息</span>
        </div>
        <div class="info-grid grid-3">
          <div class="info-item">
            <span class="label">地区</span>
            <span class="value">{{ complaintData.school.region }}</span>
          </div>
          <div class="info-item">
            <span class="label">学校</span>
            <span class="value">{{ complaintData.school.schoolName }}</span>
          </div>
          <div class="info-item">
            <span class="label">学校类型</span>
            <span class="value">{{ complaintData.school.schoolType }}</span>
          </div>
        </div>
      </div>

      <!-- 具体问题 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img
            src="@/assets/images/detail/question.png"
            alt=""
            width="12"
            height="12"
          />
          <span class="title-text">具体问题</span>
        </div>
        <div class="problem-content">
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item">
              <span class="label">投诉时间</span>
              <span class="value">{{ complaintData.problem.reportTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">分类</span>
              <span class="value">{{ complaintData.problem.category }}</span>
            </div>
          </div>

          <div class="problem-description mb-[20px]">
            <div class="description-text">
              <div class="label mb-[8px]">举报内容</div>
              {{ complaintData.problem.description }}
            </div>
          </div>

          <div class="attachments">
            <div class="label mb-[12px]">问题附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, index) in complaintData.problem.attachments"
                :key="index"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                      ? pdfIcon
                      : attachment.type === 'word' || attachment.type === 'doc'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分办信息 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img
            src="@/assets/images/detail/supervise-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">分办信息</span>
        </div>
        <div class="assignment-content">
          <div class="assignment-description">
            <div class="label mb-[8px]">分办意见</div>
            <div class="description-text">
              {{ complaintData.assignment.assignedTo }}
            </div>
          </div>
        </div>
      </div>

      <!-- 回复信息 -->
      <div class="info-section mb-[20px]" v-for="reply in complaintData.replies" :key="reply.id">
        <div class="section-title">
          <img
            src="@/assets/images/detail/answer-info.png"
            alt=""
            width="14"
            height="11"
          />
          <span class="title-text">回复信息</span>
        </div>
        <div class="reply-content">
          <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item no-padding-bg">
              <span class="label">办理单位</span>
              <span class="value">{{ reply.department }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">回复时间</span>
              <span class="value">{{ reply.replyTime }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人姓名</span>
              <span class="value">{{ reply.handlerName }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">经办人电话</span>
              <span class="value">{{ reply.handlerPhone }}</span>
            </div>
          </div>

          <!-- 回复内容单独一行 -->
          <div class="reply-description mb-[20px]">
            <div class="label mb-[8px]">回复内容</div>
            <div class="description-text no-padding-bg">{{ reply.content }}</div>
          </div>

          <!-- 回复附件单独一行 -->
          <div class="attachments" v-if="reply.attachments && reply.attachments.length > 0">
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in reply.attachments"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                      ? pdfIcon
                      : attachment.type === 'word' || attachment.type === 'doc'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="info-section mb-[20px]" v-for="review in complaintData.reviews" :key="review.id">
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-info.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">审核信息</span>
        </div>
        <div class="review-content">
          <div class="review-status mb-[5px]">
            <span class="status-label" :class="review.status === '已通过' ? 'status-passed' : 'status-failed'">{{ review.status }}</span>
            <span class="review-time">{{ review.reviewer }}</span>
          </div>

          <div class="review-description">
            <div class="description-text">{{ review.content }}</div>
          </div>
        </div>
      </div>

      <!-- 审核回复 -->
      <div class="info-section mb-[32px]" v-for="reviewReply in complaintData.reviewReplies" :key="reviewReply.id">
        <div class="section-title">
          <img
            src="@/assets/images/detail/review-answer.png"
            alt=""
            width="14"
            height="14"
          />
          <span class="title-text">审核回复</span>
        </div>
        <div class="reply-content">
          <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item no-padding-bg">
              <span class="label">办理单位</span>
              <span class="value">{{ reviewReply.department }}</span>
            </div>
            <div class="info-item no-padding-bg">
              <span class="label">回复时间</span>
              <span class="value">{{ reviewReply.replyTime }}</span>
            </div>
          </div>

          <!-- 回复内容单独一行 -->
          <div class="reply-description mb-[20px]">
            <div class="label mb-[8px]">回复内容</div>
            <div class="description-text no-padding-bg">{{ reviewReply.content }}</div>
          </div>

          <!-- 回复附件单独一行 -->
          <div class="attachments" v-if="reviewReply.attachments && reviewReply.attachments.length > 0">
            <div class="label mb-[12px]">回复附件</div>
            <div class="attachment-list">
              <div
                v-for="(attachment, attachIndex) in reviewReply.attachments"
                :key="attachIndex"
                class="attachment-item"
                @click="downloadAttachment(attachment)"
              >
                <img
                  class="attachment-icon"
                  :src="
                    attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                      ? pdfIcon
                      : attachment.type === 'word' || attachment.type === 'doc'
                      ? wordIcon
                      : ''
                  "
                />
                <span class="attachment-name">{{ attachment.name }}</span>
                <img
                  class="download-icon"
                  src="@/assets/images/detail/download.png"
                  width="15"
                  height="14"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="info-section" v-if="complaintData.finalReview">
        <div class="section-title">
          <img
            src="@/assets/images/detail/answer-info.png"
            alt=""
            width="14"
            height="11"
          />
          <span class="title-text">审核信息</span>
        </div>
        <div class="review-content">
          <div class="review-status">
            <span class="status-label" :class="complaintData.finalReview.status === '已通过' ? 'status-passed' : 'status-failed'">{{ complaintData.finalReview.status }}</span>
            <span class="review-time">{{ complaintData.finalReview.reviewer }}</span>
          </div>
        </div>
      </div>

    </section>
    <footer class="flex-end-center gap-[8px] mt-[24px]">
      <el-button class="common-button-6">
        <template #icon>
          <img src="@/assets/images/detail/back.png" alt="" width="14" height="12">
        </template>
        返回
      </el-button>
      <el-button class="common-button-6" v-if="type !== 'detail'">
        <template #icon>
          <img src="@/assets/images/detail/save.png" alt="" width="14" height="14">
        </template>
        保存
      </el-button>
      <el-button class="common-button-7" v-if="type !== 'detail'">
        <template #icon>
          <img src="@/assets/images/detail/submit.png" alt="" width="15" height="14">
        </template>
        提交
      </el-button>
    </footer>
  </div>
</template>

<style lang="scss" scoped>
.detail {
  .content {
    .info-section {
      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        .title-icon {
          width: 4px;
          height: 20px;
          background: linear-gradient(180deg, #0ec3ed 0%, #239dde 100%);
          border-radius: 2px;
          margin-right: 12px;
        }

        .title-text {
          font-size: 14px;
          line-height: 20px;
          font-weight: 600;
          color: #2b2c33;
          padding-left: 6px;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;

        &.grid-3 {
          grid-template-columns: repeat(3, 1fr);
          gap: 18px;
        }

        &.grid-2 {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
        }

        .info-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          padding: 10px 16px;
          background: #fafafa;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #e2e3e6;

          &.no-padding-bg {
            padding: 0;
            background: transparent;
            border: none;
            border-radius: 0;
          }

          .label {
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            color: #94959c;
            margin-bottom: 4px;
          }

          .value {
            font-size: 16px;
            line-height: 24px;
            color: #2b2c33;
          }
        }
      }

      .problem-content {
        .problem-description {
          .label {
            font-size: 14px;
            font-weight: 400;
            color: #94959c;
          }

          .description-text {
            font-size: 16px;
            color: #2b2c33;
            line-height: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e2e3e6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          }
        }

        .attachments {
          padding: 12px 16px;
          background: #f8f9fa;
          border: 1px solid #e2e3e6;
          border-radius: 8px;
          .label {
            font-size: 14px;
            font-weight: 400;
            color: #94959c;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      .assignment-content {
        .assignment-description {
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e2e3e6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          .label {
            font-size: 14px;
            color: #6d6f75;
          }

          .description-text {
            font-size: 16px;
            line-height: 24px;
            color: #2b2c33;
          }
        }
      }

      // 回复信息和审核回复样式
      .reply-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .reply-description {
          .label {
            font-size: 14px;
            color: #6D6F75;
          }

          .description-text {
            font-size: 16px;
            color: #2B2C33;
            line-height: 24px;
            padding: 16px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #E2E3E6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          }
        }

        .attachments {
          .label {
            font-size: 14px;
            color: #6D6F75;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      // 审核信息样式
      .review-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .review-status {
          display: flex;
          align-items: center;
          gap: 16px;

          .status-label {
            font-size: 12px;
            height: 20px;
            line-height: 20px;
            padding: 4px 8px;
            border-radius: 10px;
            display: inline-flex;
            align-items: center;

            &.status-passed {
              color: #28B28B;
              background-color: #DCFCE7;
            }

            &.status-failed {
              color: #E72C4A;
              background-color: #FFE9E9;
            }
          }

          .review-time {
            font-size: 14px;
            color: #6D6F75;
          }
        }

        .review-description {
          .description-text {
            font-size: 16px;
            color: #2B2C33;
            line-height: 24px;
          }
        }
      }
    }
  }
}
</style>
