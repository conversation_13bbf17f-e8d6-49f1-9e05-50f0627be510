import { setRouteChange } from "@/hooks/useRouteListener"
import { useTitle } from "@/hooks/useTitle"
import { getToken } from "@/utils/cache/cookies"
import NProgress from "nprogress"
import { usePermissionStore } from "@/store/modules/permission"
import { useUserStore } from "@/store/modules/user"
import { routerConfig } from "@/router/config"
import { isWhiteList } from "@/router/whitelist"

NProgress.configure({ showSpinner: false })

const { setTitle } = useTitle()

const LOGIN_PATH = "/login"

export function registerNavigationGuard(router) {
  // 全局前置守卫
  router.beforeEach(async (to, _from) => {
    NProgress.start()
    const userStore = useUserStore()
    const permissionStore = usePermissionStore()

    if (!getToken()) {
      if (isWhiteList(to)) return true
      return LOGIN_PATH
    }

    if (to.path === LOGIN_PATH) {
      // 如果已登录且有权限信息，重定向到首页
      if (userStore.roles.length > 0 && permissionStore.firstRoute) {
        return permissionStore.firstRoute
      }
      return "/"
    }

    // 处理根路径重定向
    if (to.path === "/") {
      if (userStore.roles.length > 0 && permissionStore.firstRoute) {
        return permissionStore.firstRoute
      }
      // 如果还没有角色信息，继续执行后续逻辑获取权限
    }

    if (userStore.roles.length !== 0) return true

    try {
      await userStore.getInfo()
      const roles = userStore.roles
      routerConfig.dynamic ? permissionStore.setRoutes(roles) : permissionStore.setAllRoutes()

      permissionStore.addRoutes.forEach(route => router.addRoute(route))

      // 如果当前访问的是根路径，重定向到对应身份的首页
      if (to.path === "/" && permissionStore.firstRoute) {
        return { path: permissionStore.firstRoute, replace: true }
      }

      return { ...to, replace: true }
    } catch (error) {
      userStore.resetToken()
      ElMessage.error(error.message || "路由守卫发生错误")
      return LOGIN_PATH
    }
  })

  // 全局后置钩子
  router.afterEach((to) => {
    setRouteChange(to)
    setTitle(to.meta.title)
    NProgress.done()
  })
}

