import { getCosConfigApi } from "@/apis/cos"
import COS from "cos-js-sdk-v5"

export async function getCosConfig() {
  try {
    const res = await getCosConfigApi()

    if (res) {
      const { data } = res

      const config = data

      const cosInstance = new COS({
        SecretId: data.token.credentials.tmpSecretId,
        SecretKey: data.token.credentials.tmpSecretKey,
        XCosSecurityToken: data.token.credentials.sessionToken,
        StartTime: data.token.startTime,
        ExpiredTime: data.token.expiredTime
      })

      return { cosInstance, config }
    }
  } catch (err) {
    console.warn("获取cos配置失败", err)
  }
}
