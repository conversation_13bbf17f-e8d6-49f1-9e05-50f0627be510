import { pinia } from './store'
import { router } from '@/router'
import { installPlugins } from './plugins'
import App from './App.vue'

import 'normalize.css'
import 'nprogress/nprogress.css'
import "element-plus/theme-chalk/dark/css-vars.css";
import '@/assets/styles/index.scss'
import 'virtual:uno.css'

const app = createApp(App)

installPlugins(app)
app.use(pinia).use(router)


// router 准备就绪后挂载应用
router.isReady().then(() => {
  app.mount("#app")
})
