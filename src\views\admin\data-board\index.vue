<script setup>
import { ref, computed } from "vue";
import TopTitle from "@/views/components/TopTitle/index.vue";
import Pie<PERSON>hart from "./components/PieChart.vue";
import BarChart from "./components/BarChart.vue";
import {
  Search,
  Refresh,
  Download,
  Document,
  CircleCheck,
  SuccessFilled,
  Files,
} from "@element-plus/icons-vue";

// 响应式数据
const dateRange = ref(["2025-01-15", "2025-01-21"]);
const selectedRegion = ref("");

// 统计数据
const statsData = ref({
  totalComplaints: 1248,
  processingRate: 92.3,
  approvalRate: 87.6,
  provincialSupervision: 24,
});

// 饼图数据
const complainantData = ref([
  { value: 35, name: "学生家长" },
  { value: 25, name: "管理人员" },
  { value: 20, name: "教职工" },
  { value: 20, name: "其他人员" },
]);

const schoolTypeData = ref([
  { value: 30, name: "幼儿园" },
  { value: 25, name: "小学" },
  { value: 20, name: "普通高中" },
  { value: 15, name: "中等学校" },
  { value: 10, name: "其他" },
]);

// 举报分类统计数据
const reportCategoryData = ref([
  { value: 3200, name: "师德师风" },
  { value: 2800, name: "违规收费" },
  { value: 2500, name: "内容与课程设置" },
  { value: 2200, name: "教育教学" },
  { value: 2000, name: "校园安全" },
  { value: 1800, name: "其他问题" },
]);

// 饼图颜色配置
const complainantColors = ["#5470c6", "#91cc75", "#fac858", "#ee6666"];
const schoolTypeColors = [
  "#5470c6",
  "#91cc75",
  "#fac858",
  "#ee6666",
  "#73c0de",
];

// 柱状图颜色配置
const reportCategoryColors = [
  "#5470c6",
  "#91cc75",
  "#fac858",
  "#ee6666",
  "#73c0de",
  "#3ba272"
];

// 表格筛选条件
const selectedTableRegion = ref("全部地区");

// 地区选项
const regionOptions = ref([
  { label: "全部地区", value: "全部地区" },
  { label: "南京市", value: "南京市" },
  { label: "苏州市", value: "苏州市" },
  { label: "常州市", value: "常州市" },
  { label: "无锡市", value: "无锡市" },
  { label: "南通市", value: "南通市" },
  { label: "镇江市", value: "镇江市" }
]);

// 投诉详细数据表格
const complaintDetailData = ref([
  {
    region: "南京市",
    total: 2451,
    student: 171,
    parent: 1124,
    teacher: 567,
    public: 171,
    other: 171,
    foodSafety: 171,
    mealFee: 245,
    textbook: 123,
    uniform: 89,
    training: 156,
    otherCategory: 234,
    kindergarten: 145,
    primary: 567,
    middle: 234,
    high: 123,
    university: 89,
    integrated: 67,
    vocational: 45,
    college: 23
  },
  {
    region: "苏州市",
    total: 2287,
    student: 166,
    parent: 1056,
    teacher: 523,
    public: 166,
    other: 166,
    foodSafety: 166,
    mealFee: 228,
    textbook: 114,
    uniform: 82,
    training: 145,
    otherCategory: 218,
    kindergarten: 135,
    primary: 528,
    middle: 218,
    high: 114,
    university: 82,
    integrated: 62,
    vocational: 42,
    college: 21
  },
  {
    region: "常州市",
    total: 2287,
    student: 166,
    parent: 1056,
    teacher: 523,
    public: 166,
    other: 166,
    foodSafety: 166,
    mealFee: 228,
    textbook: 114,
    uniform: 82,
    training: 145,
    otherCategory: 218,
    kindergarten: 135,
    primary: 528,
    middle: 218,
    high: 114,
    university: 82,
    integrated: 62,
    vocational: 42,
    college: 21
  },
  {
    region: "无锡市",
    total: 2287,
    student: 166,
    parent: 1056,
    teacher: 523,
    public: 166,
    other: 166,
    foodSafety: 166,
    mealFee: 228,
    textbook: 114,
    uniform: 82,
    training: 145,
    otherCategory: 218,
    kindergarten: 135,
    primary: 528,
    middle: 218,
    high: 114,
    university: 82,
    integrated: 62,
    vocational: 42,
    college: 21
  },
  {
    region: "南通市",
    total: 2287,
    student: 166,
    parent: 1056,
    teacher: 523,
    public: 166,
    other: 166,
    foodSafety: 166,
    mealFee: 228,
    textbook: 114,
    uniform: 82,
    training: 145,
    otherCategory: 218,
    kindergarten: 135,
    primary: 528,
    middle: 218,
    high: 114,
    university: 82,
    integrated: 62,
    vocational: 42,
    college: 21
  },
  {
    region: "镇江市",
    total: 287,
    student: 166,
    parent: 1056,
    teacher: 523,
    public: 166,
    other: 166,
    foodSafety: 166,
    mealFee: 28,
    textbook: 14,
    uniform: 8,
    training: 15,
    otherCategory: 22,
    kindergarten: 13,
    primary: 52,
    middle: 22,
    high: 11,
    university: 8,
    integrated: 6,
    vocational: 4,
    college: 2
  }
]);

// 计算属性 - 筛选后的表格数据
const filteredTableData = computed(() => {
  if (!selectedTableRegion.value || selectedTableRegion.value === "全部地区") {
    return complaintDetailData.value;
  }
  return complaintDetailData.value.filter(item =>
    item.region === selectedTableRegion.value
  );
});

// 方法
const handleRefresh = () => {
  console.log("刷新数据");
};

const handleExport = () => {
  console.log("导出数据");
};

const handleSearch = () => {
  console.log("搜索地区:", selectedRegion.value);
};
</script>

<template>
  <div class="data-board">
    <TopTitle title="数据看板"></TopTitle>

    <!-- 第一行：筛选条件和操作按钮 -->
    <div class="filter-section">
      <div class="filter-left">
        <!-- 时间选择 -->
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            size="default"
          />
        </div>

        <!-- 搜索地区 -->
        <div class="search-wrapper">
          <el-input
            v-model="selectedRegion"
            placeholder="搜索地区"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <div class="filter-right">
        <!-- 刷新数据按钮 -->
        <el-button class="common-button-6" @click="handleRefresh" :icon="Refresh"> 刷新数据 </el-button>

        <!-- 导出数据按钮 -->
        <el-button class="common-button-5 mx-0" @click="handleExport">
          <template #icon>
            <img
              src="@/assets/images/common/export.png"
              alt=""
              width="16"
              height="16"
            />
          </template>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 第二行：统计卡片 -->
    <div class="stats-section">
      <!-- 总投诉量 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">总投诉量</div>
          <div class="stats-value">
            {{ statsData.totalComplaints.toLocaleString() }}
          </div>
        </div>
        <div class="stats-icon complaints-icon">
          <img src="@/assets/images/data-board/1.png" alt="" />
        </div>
      </div>

      <!-- 处理率 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">处理率</div>
          <div class="stats-value">{{ statsData.processingRate }}%</div>
        </div>
        <div class="stats-icon processing-icon">
          <img src="@/assets/images/data-board/2.png" alt="" />
        </div>
      </div>

      <!-- 审核通过率 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">审核通过率</div>
          <div class="stats-value">{{ statsData.approvalRate }}%</div>
        </div>
        <div class="stats-icon approval-icon">
          <img src="@/assets/images/data-board/3.png" alt="" />
        </div>
      </div>

      <!-- 省厅督办件 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">省厅督办件</div>
          <div class="stats-value">{{ statsData.provincialSupervision }}</div>
        </div>
        <div class="stats-icon supervision-icon">
          <img src="@/assets/images/data-board/4.png" alt="" />
        </div>
      </div>
    </div>

    <!-- 图表区域第一行 -->
    <div class="chart-section-1">
      <div class="chart-row">
        <!-- 举报人身份分布 -->
        <div class="chart-item">
          <div class="text-[18px] grey1 font-semibold">举报人身份分布</div>
          <PieChart
            :chart-data="complainantData"
            :colors="complainantColors"
            height="350px"
          />
        </div>

        <!-- 学校类型分布 -->
        <div class="chart-item">
          <div class="text-[18px] grey1 font-semibold">学校类型分布</div>
          <PieChart
            :chart-data="schoolTypeData"
            :colors="schoolTypeColors"
            height="350px"
          />
        </div>
      </div>
    </div>

    <!-- 图表区域第二行 -->
    <div class="chart-section-2">
      <div class="chart-row">
        <!-- 举报分类统计 -->
        <div class="chart-item chart-item-full">
          <div class="text-[18px] grey1 font-semibold">举报分类统计</div>
          <BarChart
            :chart-data="reportCategoryData"
            :colors="reportCategoryColors"
            height="350px"
          />
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="table-header">
        <div class="table-title">
          <span class="text-[18px] grey1 font-semibold">投诉详细数据</span>
        </div>
        <div class="table-filters">
          <!-- 地区选择 -->
          <el-select
            v-model="selectedTableRegion"
            placeholder="选择地区"
            style="width: 200px"
          >
            <el-option
              v-for="option in regionOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table
          :data="filteredTableData"
          class="custom-table-2"
          style="width: 100%"
        >
          <!-- 地区列 -->
          <el-table-column prop="region" label="地区" width="100" fixed="left" />

          <!-- 总数列 -->
          <el-table-column prop="total" label="总数" width="80" />

          <!-- 举报人身份列 -->
          <el-table-column prop="student" label="学生" width="80" />
          <el-table-column prop="parent" label="学生家长" width="100" />
          <el-table-column prop="teacher" label="学校教职工" width="110" />
          <el-table-column prop="public" label="普通群众" width="100" />
          <el-table-column prop="other" label="其他人员" width="100" />

          <!-- 投诉类型列 -->
          <el-table-column prop="foodSafety" label="食品安全" width="100" />
          <el-table-column prop="mealFee" label="膳食经费" width="100" />
          <el-table-column prop="textbook" label="教辅教材征订" width="120" />
          <el-table-column prop="uniform" label="校服定制采购" width="120" />
          <el-table-column prop="training" label="校外培训" width="100" />
          <el-table-column prop="otherCategory" label="其他" width="80" />

          <!-- 学校类型列 -->
          <el-table-column prop="kindergarten" label="幼儿园" width="100" />
          <el-table-column prop="primary" label="小学" width="80" />
          <el-table-column prop="middle" label="初中" width="80" />
          <el-table-column prop="high" label="高中" width="80" />
          <el-table-column prop="university" label="大学" width="80" />
          <el-table-column prop="integrated" label="多年一贯制学校" width="140" />
          <el-table-column prop="vocational" label="中职" width="80" />
          <el-table-column prop="college" label="高职" width="80" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.data-board {
  padding: 20px;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0 16px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
      0px 1px 2px -1px rgba(0, 0, 0, 0.1);

    .filter-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .date-picker-wrapper {
        .el-date-editor {
          width: 280px;
        }
      }

      .search-wrapper {
        .el-input {
          width: 200px;
        }
      }
    }

    .filter-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;

      .el-select {
        width: 180px;
      }
    }

    :deep(.el-input) {
      .el-input__wrapper {
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px;
      }
    }

    :deep(.el-select) {
      .el-select__wrapper {
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px;
      }
    }

    :deep(.el-date-editor) {
      height: 40px !important;
      line-height: 40px !important;
    }
  }

  .stats-section {
    display: flex;
    gap: 15px;
    margin: 16px 0;

    .stats-card {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 24px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
        0px 1px 2px -1px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;

        img {
          width: 48px;
          height: 48px;
          object-fit: contain;
        }
      }

      .stats-content {
        flex: 1;

        .stats-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .stats-value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          line-height: 1;
        }
      }
    }
  }

  .chart-section-1 {
    margin: 20px 0;

    .chart-row {
      display: flex;
      gap: 20px;

      .chart-item {
        flex: 1;
        min-height: 392px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
      }
    }
  }

  .chart-section-2 {
    margin: 20px 0;

    .chart-row {
      display: flex;
      gap: 20px;

      .chart-item {
        flex: 1;
        min-height: 392px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;

        &.chart-item-full {
          width: 100%;
        }
      }
    }
  }

  .table-section {
    margin: 20px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .table-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .table-filters {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }
}
</style>
