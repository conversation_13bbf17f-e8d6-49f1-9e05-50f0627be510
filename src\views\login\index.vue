<script setup>
import { Key, Lock, User } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import { loginApi, sendSmsCodeApi, resetPasswordApi } from "./apis";
import { useFocus } from "./composables/useFocus";

const router = useRouter();

const userStore = useUserStore();

const { handleBlur, handleFocus } = useFocus();

/** 登录表单元素的引用 */
const loginFormRef = useTemplateRef("loginFormRef");

/** 登录按钮 Loading */
const loading = ref(false);

/** 手机验证码倒计时 */
const countdown = ref(0);
const countdownTimer = ref(null);

/** 登录表单数据 */
const loginFormData = reactive({
  username: "admin",
  password: "12345678",
  confirmPassword: "",
  code: "",
});

/** 登录表单校验规则 */
const loginFormRules = computed(() => {
  const baseRules = {
    username: [{ required: true, message: "请输入手机号", trigger: "blur" }],
    password: [
      { required: true, message: "请输入密码", trigger: "blur" },
      { min: 8, max: 16, message: "长度在 8 到 16 个字符", trigger: "blur" },
    ],
    code: [{ required: true, message: "请输入验证码", trigger: "blur" }],
  };

  // 重置密码模式下添加确认密码校验
  if (activeStatus.value === 2) {
    baseRules.confirmPassword = [
      { required: true, message: "请再次输入密码", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value !== loginFormData.password) {
            callback(new Error("两次输入的密码不一致"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ];
  }

  return baseRules;
});

/** 表单提交处理 */
function handleSubmit() {
  if (activeStatus.value === 1) {
    handleLogin();
  } else {
    handleResetPassword();
  }
}

/** 登录 */
function handleLogin() {
  loginFormRef.value?.validate((valid) => {
    if (!valid) {
      ElMessage.error("表单校验不通过");
      return;
    }
    loading.value = true;
    loginApi(loginFormData)
      .then(({ data }) => {
        userStore.setToken(data.token);
        router.push("/");
      })
      .catch(() => {
        loginFormData.password = "";
        loginFormData.code = "";
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

/** 发送手机验证码 */
const sendCode = async () => {
  // 验证手机号格式
  if (!loginFormData.username) {
    ElMessage.error("请先输入手机号");
    return;
  }

  // const phoneRegex = /^1[3-9]\d{9}$/;
  // if (!phoneRegex.test(loginFormData.username)) {
  //   ElMessage.error("请输入正确的手机号");
  //   return;
  // }

  try {
    // 调用发送验证码API
    // await sendSmsCodeApi({ phone: loginFormData.username });

    // 开始倒计时
    countdown.value = 60;
    countdownTimer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
    }, 1000);

    ElMessage.success("验证码已发送");
  } catch (error) {
    ElMessage.error("验证码发送失败，请重试");
  }
};

/** 验证码按钮文本 */
const codeButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}秒` : "获取手机验证码";
});

/** 验证码按钮是否禁用 */
const codeButtonDisabled = computed(() => {
  return countdown.value > 0;
});

// 1、登录 2、重置密码
const activeStatus = ref(1);

/** 切换登录/重置密码状态 */
const changeStatus = () => {
  if (activeStatus.value === 1) {
    activeStatus.value = 2;
  } else {
    activeStatus.value = 1;
    // 切换回登录时清空表单
    clearForm();
  }
};

/** 清空表单 */
const clearForm = () => {
  loginFormData.username = "";
  loginFormData.password = "";
  loginFormData.confirmPassword = "";
  loginFormData.code = "";
  // 清除验证错误
  loginFormRef.value?.clearValidate();
};

/** 重置密码 */
const handleResetPassword = async () => {
  loginFormRef.value?.validate(async (valid) => {
    if (!valid) {
      ElMessage.error("表单校验不通过");
      return;
    }

    if (loginFormData.password !== loginFormData.confirmPassword) {
      ElMessage.error("两次输入的密码不一致");
      return;
    }

    loading.value = true;
    try {
      await resetPasswordApi({
        username: loginFormData.username,
        password: loginFormData.password,
        confirmPassword: loginFormData.confirmPassword,
        code: loginFormData.code,
      });
      ElMessage.success("密码重置成功");
      clearForm();
      activeStatus.value = 1;
    } catch (error) {
      ElMessage.error("密码重置失败，请重试");
    } finally {
      loading.value = false;
    }
  });
};

/** 提示文字 */
const tipText = computed(() => {
  return activeStatus.value === 1 ? "请登录你的账号" : "重置密码";
});

/** 提交按钮文字 */
const submitButtonText = computed(() => {
  return activeStatus.value === 1 ? "登 录" : "确认修改";
});

/** 右侧操作文字 */
const actionText = computed(() => {
  return activeStatus.value === 1 ? "重置密码" : "返回登录";
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});
</script>

<template>
  <div class="login-container">
    <div class="left">
      <img
        class="login-logo"
        src="@/assets/images/login/login-logo.png"
        alt=""
      />
      <div></div>
      <div class="middle flex-column-center-center">
        <div class="title bolder">教育问题 “码” 上监督</div>
        <div class="title">江苏省教育厅整治投诉平台</div>
      </div>
      <div class="bottom flex-center-center">
        <div class="bottom-left flex-column-center-center">
          <div class="unit">中共江苏省委教育工委 江苏省教育厅</div>
          <div class="unit">江苏省纪委监委驻省教育厅纪检监察组</div>
        </div>
        <div class="bottom-right">主办</div>
      </div>
    </div>
    <div class="login-form">
      <div class="title">整治投诉平台管理系统</div>
      <div class="tip flex-between-center">
        <div class="tip-text">{{ tipText }}</div>
        <div v-if="activeStatus === 2" class="right flex-end-center" @click="changeStatus">
          <img
            class="mr-[8px]"
            src="@/assets/images/login/back.png"
            alt=""
            width="14"
            height="16"
          />
          {{ actionText }}
        </div>
      </div>
      <div class="content">
        <el-form
          ref="loginFormRef"
          :model="loginFormData"
          :rules="loginFormRules"
          @keyup.enter="handleSubmit"
        >
          <el-form-item prop="username">
            <el-input
              v-model.trim="loginFormData.username"
              placeholder="手机号"
              type="text"
              tabindex="1"
              :prefix-icon="User"
              size="large"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model.trim="loginFormData.password"
              placeholder="密码"
              type="password"
              tabindex="2"
              :prefix-icon="Lock"
              size="large"
              show-password
              @blur="handleBlur"
              @focus="handleFocus"
            />
          </el-form-item>
          <el-form-item v-if="activeStatus === 2" prop="confirmPassword">
            <el-input
              v-model.trim="loginFormData.confirmPassword"
              placeholder="再次输入确认密码"
              type="password"
              tabindex="3"
              :prefix-icon="Lock"
              size="large"
              show-password
              @blur="handleBlur"
              @focus="handleFocus"
            />
          </el-form-item>
          <el-form-item prop="code">
            <el-input
              v-model.trim="loginFormData.code"
              placeholder="手机验证码"
              type="text"
              :tabindex="activeStatus === 2 ? 4 : 3"
              :prefix-icon="Key"
              size="large"
              @blur="handleBlur"
              @focus="handleFocus"
            >
              <template #append>
                <el-button
                  class="code-btn"
                  :disabled="codeButtonDisabled"
                  @click="sendCode"
                  style="border: none; border-radius: 0"
                >
                  {{ codeButtonText }}
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-button
            class="login-submit-btn"
            :loading="loading"
            type="primary"
            size="large"
            @click.prevent="handleSubmit"
          >
            {{ submitButtonText }}
          </el-button>
        </el-form>
      </div>
      <div
        class="mt-[14px] reset-btn"
        v-if="activeStatus === 1"
        @click="changeStatus"
      >
        {{ actionText }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  background-color: #fff;

  .left {
    position: relative;
    display: flex;
    flex: 1;
    background: url("@/assets/images/login/login-bg.png") no-repeat;
    background-size: 100% 100%;

    .login-logo {
      position: absolute;
      top: 9.4%;
      left: 10.7%;
      width: 31.5%;
      height: 6.15%;
      object-fit: contain;
    }

    .middle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;

      .title {
        font-weight: 500;
        font-size: 48px;
        color: #ffffff;
        line-height: 70px;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.4064);
        text-transform: none;

        &.bolder {
          font-weight: bold;
          padding-bottom: 18px;
        }
      }
    }

    .bottom {
      position: absolute;
      bottom: 19.6%;
      gap: 22px;
      width: 100%;
      display: flex;
      justify-content: center;

      .bottom-left {
        .unit {
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 35px;
          text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.7203);
        }
      }

      .bottom-right {
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 35px;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.7203);
      }
    }
  }

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 600px;
    padding: 0 124px;
    background-color: var(--el-bg-color);
    overflow: hidden;

    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      padding-bottom: 34px;
      font-weight: 600;
      font-size: 24px;
      color: #05afe8;
      line-height: 35px;
      text-align: left;
    }

    .tip {
      width: 100%;
      font-weight: 600;
      font-size: 16px;
      color: #2b2c33;
      line-height: 23px;
      text-align: left;
      padding-bottom: 25px;

      .right {
        font-weight: 400;
        font-size: 16px;
        color: #4b5563;
        line-height: 24px;
        cursor: pointer;
      }
    }

    .content {
      width: 100%;

      :deep(.el-input-group__append) {
        overflow: hidden;
      }

      :deep(.el-form) {
        width: 100%;
      }

      .login-submit-btn {
        width: 100%;
        margin-top: 10px;
        background: linear-gradient(270deg, #36b8fc 0%, #02afe7 100%), #09b1ea;
        border-radius: 4px 4px 4px 4px;
      }

      .code-btn {
        width: 130px;
      }
    }

    .reset-btn {
      font-weight: 400;
      font-size: 13px;
      color: #069acb;
      line-height: 22px;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
